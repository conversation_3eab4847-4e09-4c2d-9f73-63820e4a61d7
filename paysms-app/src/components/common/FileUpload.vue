<template>
  <div class="space-y-3">
    <!-- Upload Area -->
    <div
      @click="triggerFileInput"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
      :class="[
        'border-2 border-dashed rounded-xl p-6 text-center cursor-pointer transition-colors',
        isDragging
          ? 'border-blue-500 bg-blue-50'
          : hasError
          ? 'border-red-300 bg-red-50'
          : 'border-gray-300 hover:border-gray-400'
      ]"
    >
      <!-- Upload Icon and Text -->
      <div v-if="!selectedFile">
        <Upload class="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p class="text-sm font-medium text-gray-700 mb-1">
          {{ uploadText || 'Click to upload or drag and drop' }}
        </p>
        <p class="text-xs text-gray-500">
          {{ acceptText }} ({{ maxSizeText || 'Max' }} {{ maxSize }}MB)
        </p>
      </div>

      <!-- Selected File Preview -->
      <div v-else class="flex items-center justify-center space-x-3">
        <div class="flex items-center space-x-2">
          <component :is="getFileIcon(selectedFile.type)" class="w-6 h-6 text-blue-600" />
          <div class="text-left">
            <p class="text-sm font-medium text-gray-900">
              {{ selectedFile.name }}
            </p>
            <p class="text-xs text-gray-500">
              {{ formatFileSize(selectedFile.size) }}
            </p>
          </div>
        </div>
        <button
          @click.stop="removeFile"
          class="p-1 text-gray-400 hover:text-red-500 transition-colors"
        >
          <X class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- Hidden File Input -->
    <input ref="fileInput" type="file" :accept="accept" @change="handleFileSelect" class="hidden" />

    <!-- Upload Progress -->
    <div v-if="isUploading" class="space-y-2">
      <div class="flex items-center justify-between text-sm">
        <span class="text-gray-600">{{ uploadingText || 'Uploading...' }}</span>
        <span class="text-gray-600">{{ uploadProgress }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${uploadProgress}%` }"
        />
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="errorMessage" class="text-sm text-red-600">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Upload, X, FileText, Image, File } from 'lucide-vue-next'

interface Props {
  modelValue: File | null
  accept?: string
  maxSize?: number // in MB
  multiple?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  accept: '*/*',
  maxSize: 5,
  multiple: false
})

const emit = defineEmits<{
  'update:modelValue': [file: File | null]
  upload: [file: File]
  error: [error: string]
}>()

// State
const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(props.modelValue)
const isDragging = ref(false)
const isUploading = ref(false)
const uploadProgress = ref(0)
const errorMessage = ref('')

// Computed
const hasError = computed(() => !!errorMessage.value)

const acceptText = computed(() => {
  if (props.accept === 'image/*') {
    return props.imagesText || 'Images'
  } else if (props.accept.includes('.pdf')) {
    return props.imagesPdfText || 'Images or PDF'
  } else {
    return props.filesText || 'Files'
  }
})

// Methods
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    validateAndSetFile(file)
  }
}

const handleDragOver = () => {
  isDragging.value = true
}

const handleDragLeave = () => {
  isDragging.value = false
}

const handleDrop = (event: DragEvent) => {
  isDragging.value = false
  const file = event.dataTransfer?.files[0]
  if (file) {
    validateAndSetFile(file)
  }
}

const validateAndSetFile = (file: File) => {
  errorMessage.value = ''

  // Check file size
  const fileSizeMB = file.size / (1024 * 1024)
  if (fileSizeMB > props.maxSize) {
    errorMessage.value = props.fileSizeErrorText || `File size must be less than ${props.maxSize}MB`
    emit('error', errorMessage.value)
    return
  }

  // Check file type
  if (props.accept !== '*/*' && !isFileTypeAccepted(file.type)) {
    errorMessage.value = 'File type not supported'
    emit('error', errorMessage.value)
    return
  }

  selectedFile.value = file
  emit('update:modelValue', file)

  // Simulate upload
  simulateUpload(file)
}

const isFileTypeAccepted = (fileType: string): boolean => {
  if (props.accept === 'image/*') {
    return fileType.startsWith('image/')
  }

  const acceptedTypes = props.accept.split(',').map((type) => type.trim())
  return acceptedTypes.some((acceptedType) => {
    if (acceptedType.startsWith('.')) {
      return fileType.includes(acceptedType.substring(1))
    }
    return fileType === acceptedType || fileType.startsWith(acceptedType.split('/')[0] + '/')
  })
}

const simulateUpload = async (file: File) => {
  isUploading.value = true
  uploadProgress.value = 0

  // Simulate upload progress
  const interval = setInterval(() => {
    uploadProgress.value += 10
    if (uploadProgress.value >= 100) {
      clearInterval(interval)
      isUploading.value = false
      emit('upload', file)
    }
  }, 200)
}

const removeFile = () => {
  selectedFile.value = null
  errorMessage.value = ''
  uploadProgress.value = 0
  isUploading.value = false
  emit('update:modelValue', null)

  // Reset file input
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const getFileIcon = (fileType: string) => {
  if (fileType.startsWith('image/')) {
    return Image
  } else if (fileType === 'application/pdf') {
    return FileText
  } else {
    return File
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>
